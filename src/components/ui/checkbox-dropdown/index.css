.custom-select {
  position: relative;
  width: fit-content;
  box-sizing: border-box;
}

.select-box {
  border-radius: 8px;
  padding: 6px 10px;
  cursor: pointer;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.select-box.open {
  border: 1px solid #0000001a;
  background-color: #000000cc;
  color: white;
  outline: none;
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 8px;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0px 4px 25px 0px #0000000d;
}

.select-option {
  cursor: pointer;
}

.select-option:hover {
  background-color: #f0f0f0;
}

.select-option.selected {
  background-color: #e6f4ff;
  font-weight: bold;
}

.arrow {
  font-size: 10px;
}
