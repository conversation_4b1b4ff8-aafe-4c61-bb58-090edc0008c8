import {
  FilterType,
  type FilterItem,
  type FilterOptionValue,
} from "@/clients/gen/broking/FilterSearch_pb";
import type { AppliedFilter, FilterOptionValueWithLabel } from "../../machine";
import Chip from "@/components/ui/chip/chip";
import Checkbox from "@/components/ui/checkbox/checkbox";
import Switch from "@/components/ui/switch/switch";

interface ProgressiveFilterRendererProps {
  item: FilterItem;
  handleFilter: (
    filters: FilterOptionValue,
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
}

const ProgressiveFilterRenderer = ({
  item,
  handleFilter,
  appliedFilters,
}: ProgressiveFilterRendererProps) => {
  const renderFilterContent = () => {
    switch (item.type) {
      case FilterType.MULTI_SELECT_PILLS:
        return (
          <div className="mt-3 flex flex-wrap gap-2">
            {item.options.map((option, index) => (
              <Chip
                key={`${item.key}-${index}`}
                selected={appliedFilters.some(
                  (filter) =>
                    filter.key === item.key &&
                    filter.filters.includes(
                      option.optionValue as FilterOptionValueWithLabel
                    )
                )}
                size="medium"
                variant="outlined"
                onClick={() => {
                  handleFilter(option.optionValue!, !option.isSelected, index);
                }}
              >
                {option.label}
              </Chip>
            ))}
          </div>
        );
      case FilterType.RANGE_MULTI_SELECT:
      case FilterType.MULTI_SELECT:
        return (
          <div className="mt-3 space-y-3">
            {item.options.map((option, index) => (
              <Checkbox
                key={`${item.key}-${index}`}
                name={`${item.key}-${index}`}
                defaultChecked={appliedFilters.some(
                  (filter) =>
                    filter.key === item.key &&
                    filter.filters.includes(
                      option.optionValue as FilterOptionValueWithLabel
                    )
                )}
                className="text-black-80 flex-row-reverse justify-between"
                onChange={(event) => {
                  handleFilter(
                    option.optionValue!,
                    (event.target as HTMLInputElement).checked,
                    index
                  );
                }}
              >
                <p className="text-body1">{option.label}</p>
              </Checkbox>
            ))}
          </div>
        );

      case FilterType.MULTI_SELECT_WITH_ICON:
        return (
          <div className="mt-3 flex flex-wrap gap-2.5 space-y-2">
            {item.options.map((option, index) => (
              <div
                key={`${item.key}-${index}`}
                className="text-body1 flex items-center gap-2"
              >
                <Chip
                  selected={appliedFilters.some(
                    (filter) =>
                      filter.key === item.key &&
                      filter.filters.includes(
                        option.optionValue as FilterOptionValueWithLabel
                      )
                  )}
                  onClick={(isSelected) => {
                    handleFilter(
                      option.optionValue!,
                      isSelected as boolean,
                      index
                    );
                  }}
                >
                  <img src={option.icon} className="h-4 w-4 object-contain" />
                  {option.label}
                </Chip>
              </div>
            ))}
          </div>
        );

      case FilterType.BOOLEAN_SELECT:
        return null;

      default:
        return (
          <div className="mt-3 space-y-3">
            {item.options.map((option, index) => (
              <Checkbox
                key={`${item.key}-${index}`}
                name={`${item.key}-${index}`}
                defaultChecked={option.isSelected}
                className="text-body1 flex-row-reverse justify-between"
                onChange={(event) => {
                  handleFilter(
                    option.optionValue!,
                    (event.target as HTMLInputElement).checked,
                    index
                  );
                }}
              >
                {option.label}
              </Checkbox>
            ))}
          </div>
        );
    }
  };

  if (item.type === FilterType.BOOLEAN_SELECT) {
    return (
      <div className="py-4">
        <div className="flex items-center justify-between">
          <span className="text-body1 text-black-80">{item.label}</span>
          <Switch
            name={item.key}
            defaultChecked={item.options[0]?.isSelected || false}
            onChange={(event) => {
              if (item.options[0]?.optionValue) {
                handleFilter(
                  item.options[0].optionValue,
                  (event.target as HTMLInputElement).checked,
                  0
                );
              }
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="py-4">
      <div className="space-y-2">
        <h4 className="text-heading4 text-black-80">{item.label}</h4>
        {renderFilterContent()}
      </div>
    </div>
  );
};

export default ProgressiveFilterRenderer;
